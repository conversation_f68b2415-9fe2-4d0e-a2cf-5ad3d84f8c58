<!-- Personal Post Creation Modal -->
<div id="personal-post-modal" class="fixed inset-0 z-50 overflow-y-auto hidden" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <!-- Background overlay -->
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onclick="closePersonalPostModal()"></div>

        <!-- This element is to trick the browser into centering the modal contents. -->
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

        <!-- Modal panel -->
        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="sm:flex sm:items-start">
                    <div class="w-full">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4" id="modal-title">
                            Create Personal Post
                        </h3>

                        <form id="personal-post-form" action="<?php echo e(route('posts.store')); ?>" method="POST" enctype="multipart/form-data">
                            <?php echo csrf_field(); ?>

                            <!-- User Info -->
                            <div class="flex items-center space-x-3 mb-4">
                                <img class="h-10 w-10 rounded-full" src="<?php echo e(auth()->user()->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url(auth()->user()->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode(auth()->user()->name) . '&color=7BC74D&background=EEEEEE'); ?>" alt="<?php echo e(auth()->user()->name); ?>">
                                <div>
                                    <p class="font-medium text-gray-900"><?php echo e(auth()->user()->name); ?></p>
                                    <select name="organization_id" id="personal_organization_select" class="text-sm text-gray-600 border-0 bg-transparent focus:ring-0 pt-0 pb-0 pl-0" onchange="loadPersonalContextTags()">
                                        <option value="">Personal</option>
                                        <?php $__currentLoopData = auth()->user()->activeOrganizations()->get(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $org): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($org->id); ?>"><?php echo e($org->name); ?></option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>
                            </div>

                            <!-- Error Messages -->
                            <div id="personal-error-messages" class="hidden mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded"></div>

                            <!-- Title -->
                            <div class="mb-4">
                                <input type="text" name="title" placeholder="What's the title of your post?" class="w-full border-gray-300 rounded-md shadow-sm focus:ring-custom-green focus:border-custom-green" required>
                            </div>

                            <!-- Tags -->
                            <div class="mb-4" id="personal_tags_section">
                                <label class="block text-sm font-medium text-gray-700 mb-2">Tags</label>
                                <div id="personal_tags_container" class="space-y-2 max-h-32 overflow-y-auto pl-2">
                                    <!-- Tags will be loaded dynamically based on context -->
                                </div>
                            </div>

                            <!-- Content -->
                            <div class="mb-4">
                                <textarea name="content" rows="4" placeholder="What's on your mind?" class="w-full border-gray-300 rounded-md shadow-sm focus:ring-custom-green focus:border-custom-green" required></textarea>
                            </div>

                            <!-- Image Upload -->
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-2">Images</label>
                                <input type="file" name="images[]" multiple accept="image/*" class="w-full border-gray-300 rounded-md shadow-sm focus:ring-custom-green focus:border-custom-green" onchange="previewPersonalImages(this)">
                                <div id="personal-image-preview" class="mt-2 hidden"></div>
                            </div>

                            <!-- Facebook Embed URL -->
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-2">Facebook Post URL (Optional)</label>
                                <input type="url" name="facebook_embed_url" placeholder="https://www.facebook.com/..." class="w-full border-gray-300 rounded-md shadow-sm focus:ring-custom-green focus:border-custom-green">
                            </div>

                            <!-- Status -->
                            <input type="hidden" name="status" value="published">
                        </form>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button type="submit" form="personal-post-form" id="personal-submit-btn" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-custom-green text-base font-medium text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-custom-green sm:ml-3 sm:w-auto sm:text-sm">
                    <span id="personal-submit-text">Post</span>
                    <span id="personal-loading-text" class="hidden">Posting...</span>
                </button>
                <button type="button" onclick="closePersonalPostModal()" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                    Cancel
                </button>
            </div>
        </div>
    </div>
</div>

<script>
function previewPersonalImages(input) {
    const preview = document.getElementById('personal-image-preview');
    preview.innerHTML = '';
    
    if (input.files && input.files.length > 0) {
        preview.classList.remove('hidden');
        Array.from(input.files).forEach((file, index) => {
            if (file.type.startsWith('image/')) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const div = document.createElement('div');
                    div.className = 'relative inline-block mr-2 mb-2';
                    div.innerHTML = `
                        <img src="${e.target.result}" class="h-20 w-20 object-cover rounded-lg">
                        <button type="button" onclick="removePersonalImage(${index}, this)" class="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600">
                            ×
                        </button>
                    `;
                    preview.appendChild(div);
                };
                reader.readAsDataURL(file);
            }
        });
    } else {
        preview.classList.add('hidden');
    }
}

function removePersonalImage(index, button) {
    const input = document.querySelector('input[name="images[]"]');
    const dt = new DataTransfer();
    
    Array.from(input.files).forEach((file, i) => {
        if (i !== index) {
            dt.items.add(file);
        }
    });
    
    input.files = dt.files;
    button.parentElement.remove();
    
    if (input.files.length === 0) {
        document.getElementById('personal-image-preview').classList.add('hidden');
    }
}

// Handle form submission with AJAX
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('personal-post-form');
    if (form) {
        form.addEventListener('submit', async function(e) {
            e.preventDefault();

            const submitBtn = document.getElementById('personal-submit-btn');
            const submitText = document.getElementById('personal-submit-text');
            const loadingText = document.getElementById('personal-loading-text');
            const errorDiv = document.getElementById('personal-error-messages');

            // Show loading state
            submitBtn.disabled = true;
            submitText.classList.add('hidden');
            loadingText.classList.remove('hidden');
            errorDiv.classList.add('hidden');

            try {
                const formData = new FormData(form);
                const response = await fetch(form.action, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });

                if (response.ok) {
                    // Success - close modal and refresh page or update feed
                    closePersonalPostModal();
                    window.location.reload(); // Simple refresh for now
                } else {
                    const errorData = await response.json();
                    let errorMessage = 'An error occurred while creating the post.';
                    
                    if (errorData.errors) {
                        errorMessage = Object.values(errorData.errors).flat().join('<br>');
                    } else if (errorData.message) {
                        errorMessage = errorData.message;
                    }
                    
                    errorDiv.innerHTML = errorMessage;
                    errorDiv.classList.remove('hidden');
                }
            } catch (error) {
                errorDiv.innerHTML = '<p class="text-sm text-red-600">Network error. Please try again.</p>';
                errorDiv.classList.remove('hidden');
    } finally {
        const submitBtn = document.getElementById('personal-submit-btn');
        const submitText = document.getElementById('personal-submit-text');
        const loadingText = document.getElementById('personal-loading-text');

        submitBtn.disabled = false;
        submitText.classList.remove('hidden');
        loadingText.classList.add('hidden');
    }
        });
    }
});

// Simple modal functions
function openPersonalPostModal() {
    document.getElementById('personal-post-modal').classList.remove('hidden');
    // Load tags for default context (personal post)
    loadPersonalContextTags();
}

function closePersonalPostModal() {
    document.getElementById('personal-post-modal').classList.add('hidden');
    document.getElementById('personal-post-form').reset();
    document.getElementById('personal-image-preview').innerHTML = '';
    document.getElementById('personal-image-preview').classList.add('hidden');
    document.getElementById('personal-error-messages').classList.add('hidden');

    // Reset file upload zones
    const attachmentPreviews = document.querySelectorAll('#personal-post-modal [id$="-preview"]');
    attachmentPreviews.forEach(preview => {
        preview.classList.add('hidden');
        preview.innerHTML = '';
    });

    // Reset tags
    const tagCheckboxes = document.querySelectorAll('#personal_tags_container input[type="checkbox"]');
    tagCheckboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
}

// Load tags based on context (organization selection)
async function loadPersonalContextTags() {
    const organizationSelect = document.getElementById('personal_organization_select');
    const tagsContainer = document.getElementById('personal_tags_container');
    
    const organizationId = organizationSelect.value;
    
    // Determine post method based on context
    let postMethodSlug = 'user'; // Default for personal posts
    if (organizationId) {
        postMethodSlug = 'organization';
    }

    try {
        // Get the post method
        const postMethods = <?php echo json_encode(\App\Models\PostMethod::with('activeTags')->active()->get(), 15, 512) ?>;
        const postMethod = postMethods.find(method => method.slug === postMethodSlug);
        
        if (postMethod && postMethod.active_tags && postMethod.active_tags.length > 0) {
            tagsContainer.innerHTML = '';
            
            // Create checkboxes for each tag
            postMethod.active_tags.forEach(tag => {
                const tagDiv = document.createElement('div');
                tagDiv.className = 'flex items-center';
                tagDiv.innerHTML = `
                    <input type="checkbox" name="tags[]" value="${tag.id}" id="personal_tag_${tag.id}" 
                           class="h-4 w-4 text-custom-green focus:ring-custom-green border-gray-300 rounded">
                    <label for="personal_tag_${tag.id}" class="ml-2 text-sm text-gray-700 flex items-center">
                        <span class="inline-block w-3 h-3 rounded-full mr-2" style="background-color: ${tag.color}"></span>
                        ${tag.name}
                    </label>
                `;
                tagsContainer.appendChild(tagDiv);
            });
        } else {
            tagsContainer.innerHTML = '<p class="text-sm text-gray-500">No tags available for this context.</p>';
        }
    } catch (error) {
        console.error('Error loading tags:', error);
        tagsContainer.innerHTML = '<p class="text-sm text-red-500">Error loading tags.</p>';
    }
}
</script>
<?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views/components/personal-post-creation-modal.blade.php ENDPATH**/ ?>