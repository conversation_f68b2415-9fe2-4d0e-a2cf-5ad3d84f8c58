@props(['share'])

<div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6"
     data-share-id="{{ $share->id }}"
     data-timestamp="{{ $share->created_at->timestamp }}">
    <!-- Share Header -->
    <div class="p-4 border-b border-gray-100">
        <div class="flex items-center space-x-3">
            <a href="{{ route('profile.user', $share->user) }}">
                <img class="h-10 w-10 rounded-full" 
                     src="{{ $share->user->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($share->user->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($share->user->name) . '&color=7BC74D&background=EEEEEE' }}" 
                     alt="{{ $share->user->name }}">
            </a>
            <div class="flex-1">
                <div class="flex items-center space-x-2">
                    <a href="{{ route('profile.user', $share->user) }}" class="font-medium text-gray-900 hover:text-custom-green">
                        {{ $share->user->name }}
                    </a>
                    <span class="text-gray-500">shared a post</span>
                </div>
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-2 text-sm text-gray-500">
                        <time datetime="{{ $share->created_at->toISOString() }}">
                            {{ $share->created_at->diffForHumans() }}
                        </time>
                        <span>•</span>

                        <!-- Privacy Scope Indicator -->
                        @php
                            $scopeDetails = $share->getPrivacyScopeDetails();
                        @endphp
                        <div class="flex items-center space-x-1" title="{{ $scopeDetails['description'] }}">
                            @if($share->privacy_scope === 'public')
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            @elseif($share->privacy_scope === 'friends')
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                                </svg>
                            @elseif($share->privacy_scope === 'only_me')
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                                </svg>
                            @else
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                </svg>
                            @endif
                            <span class="text-xs">{{ $scopeDetails['label'] }}</span>
                        </div>
                    </div>

                    <!-- Share Actions Dropdown -->
                    @if(auth()->check() && (auth()->id() === $share->user_id || auth()->user()->isAdmin()))
                        <div class="relative" x-data="{ open: false }">
                            <button @click="open = !open" class="text-gray-400 hover:text-gray-600">
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"/>
                                </svg>
                            </button>

                            <div x-show="open" @click.away="open = false" x-transition
                                 class="absolute right-0 top-8 w-48 bg-white rounded-md shadow-lg border border-gray-200 py-1 z-10">
                                <button onclick="editShareMessage({{ $share->id }})"
                                        class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center space-x-2">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                    </svg>
                                    <span>Edit message</span>
                                </button>
                                <button onclick="deleteShare({{ $share->id }})"
                                        class="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 flex items-center space-x-2">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                    </svg>
                                    <span>Delete share</span>
                                </button>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
        
        <!-- Share Message (if any) -->
        @if($share->message)
            <div class="mt-3 text-gray-700" id="share-message-{{ $share->id }}">
                <p>{!! nl2br(e($share->message)) !!}</p>
            </div>
        @endif

        <!-- Edit Share Message Form (hidden by default) -->
        <div class="mt-3 hidden" id="edit-share-form-{{ $share->id }}">
            <form class="edit-share-form" data-share-id="{{ $share->id }}">
                @csrf
                @method('PUT')

                <!-- Privacy Scope Selector for Edit -->
                <div class="mb-3">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Privacy</label>
                    <div class="relative" x-data="{ open: false, selected: '{{ $share->privacy_scope }}' }">
                        <button type="button" @click="open = !open"
                                class="w-full flex items-center justify-between px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                            <div class="flex items-center space-x-2">
                                <svg x-show="selected === 'public'" class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                <svg x-show="selected === 'friends'" class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                                </svg>
                                <svg x-show="selected === 'only_me'" class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                                </svg>
                                <svg x-show="selected === 'custom'" class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                </svg>
                                <span x-text="selected === 'public' ? 'Public' : selected === 'friends' ? 'Friends' : selected === 'only_me' ? 'Only me' : 'Custom'"></span>
                            </div>
                            <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                            </svg>
                        </button>

                        <div x-show="open" @click.away="open = false" x-transition
                             class="absolute z-10 mt-1 w-full bg-white shadow-lg rounded-md py-1 text-base ring-1 ring-black ring-opacity-5">
                            <div @click="selected = 'public'; open = false" class="cursor-pointer py-2 pl-3 pr-9 hover:bg-gray-50">
                                <div class="flex items-center space-x-2">
                                    <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                    <span class="text-sm">Public</span>
                                </div>
                            </div>
                            <div @click="selected = 'friends'; open = false" class="cursor-pointer py-2 pl-3 pr-9 hover:bg-gray-50">
                                <div class="flex items-center space-x-2">
                                    <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                                    </svg>
                                    <span class="text-sm">Friends</span>
                                </div>
                            </div>
                            <div @click="selected = 'only_me'; open = false" class="cursor-pointer py-2 pl-3 pr-9 hover:bg-gray-50">
                                <div class="flex items-center space-x-2">
                                    <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                                    </svg>
                                    <span class="text-sm">Only me</span>
                                </div>
                            </div>
                            <div @click="selected = 'custom'; open = false" class="cursor-pointer py-2 pl-3 pr-9 hover:bg-gray-50">
                                <div class="flex items-center space-x-2">
                                    <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                    </svg>
                                    <span class="text-sm">Custom</span>
                                </div>
                            </div>
                        </div>

                        <input type="hidden" name="privacy_scope" :value="selected">
                    </div>
                </div>

                <textarea name="message" rows="3"
                          placeholder="Edit your message..."
                          class="w-full border-gray-300 rounded-md shadow-sm focus:ring-custom-green focus:border-custom-green resize-none">{{ $share->message }}</textarea>
                <div class="mt-2 flex justify-end space-x-2">
                    <button type="button" onclick="cancelEditShare({{ $share->id }})"
                            class="px-3 py-1 text-sm text-gray-600 hover:text-gray-800">Cancel</button>
                    <button type="submit"
                            class="px-3 py-1 bg-custom-green text-white text-sm font-medium rounded hover:bg-custom-second-darkest">
                        Save
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Original Post Content (Embedded) -->
    <div class="mx-4 mb-4 border border-gray-200 rounded-lg overflow-hidden">
        <!-- Original Post Header -->
        <div class="p-4 bg-gray-50">
            <div class="flex items-center space-x-3">
                @if($share->post->group)
                    <!-- Group Post Header (Facebook Style) -->
                    <div class="flex items-center space-x-2">
                        <!-- Group Avatar -->
                        <a href="{{ route('groups.show', $share->post->group) }}">
                            <img class="h-8 w-8 rounded-full"
                                 src="{{ $share->post->group->logo ? \Illuminate\Support\Facades\Storage::disk('public')->url($share->post->group->logo) : 'https://ui-avatars.com/api/?name=' . urlencode($share->post->group->name) . '&color=3B82F6&background=DBEAFE' }}"
                                 alt="{{ $share->post->group->name }}">
                        </a>

                        <!-- User Avatar (smaller, positioned to overlap slightly) -->
                        <div class="relative -ml-1">
                            <a href="{{ route('profile.user', $share->post->user) }}">
                                <img class="h-6 w-6 rounded-full border-2 border-white hover:opacity-80 transition-opacity"
                                     src="{{ $share->post->user->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($share->post->user->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($share->post->user->name) . '&color=7BC74D&background=EEEEEE' }}"
                                     alt="{{ $share->post->user->name }}">
                            </a>
                        </div>
                    </div>

                    <div class="flex-1">
                        <div class="flex items-center space-x-2">
                            <a href="{{ route('groups.show', $share->post->group) }}" class="font-medium text-gray-900 hover:text-custom-green">
                                {{ $share->post->group->name }}
                            </a>
                        </div>
                        <div class="flex items-center space-x-1 text-sm text-gray-500">
                            <a href="{{ route('profile.user', $share->post->user) }}" class="hover:text-custom-green font-medium">
                                {{ $share->post->user->name }}
                            </a>
                            <span>•</span>
                            <span>{{ $share->post->published_at->diffForHumans() }}</span>
                            @if($share->post->group->visibility === 'private')
                                <span>•</span>
                                <svg class="w-3 h-3 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 616 0z" clip-rule="evenodd" />
                                </svg>
                            @endif
                        </div>
                    </div>
                @elseif($share->post->organization)
                    <!-- Organization Post Header -->
                    <a href="{{ route('organizations.show', $share->post->organization) }}">
                        <img class="h-8 w-8 rounded-full"
                             src="{{ $share->post->organization->logo ? \Illuminate\Support\Facades\Storage::disk('public')->url($share->post->organization->logo) : 'https://ui-avatars.com/api/?name=' . urlencode($share->post->organization->name) . '&color=3B82F6&background=DBEAFE' }}"
                             alt="{{ $share->post->organization->name }}">
                    </a>
                    <div class="flex-1">
                        <div class="flex items-center space-x-2">
                            <a href="{{ route('organizations.show', $share->post->organization) }}" class="font-medium text-gray-900 hover:text-custom-green">
                                {{ $share->post->organization->name }}
                            </a>
                            <span class="text-gray-500 text-sm">•</span>
                            <a href="{{ route('profile.user', $share->post->user) }}" class="text-sm text-gray-600 hover:text-custom-green">
                                by {{ $share->post->user->name }}
                            </a>
                        </div>
                        <div class="text-sm text-gray-500">
                            {{ $share->post->published_at->diffForHumans() }}
                        </div>
                    </div>
                @else
                    <!-- Regular User Post Header -->
                    <a href="{{ route('profile.user', $share->post->user) }}">
                        <img class="h-8 w-8 rounded-full"
                             src="{{ $share->post->user->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($share->post->user->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($share->post->user->name) . '&color=7BC74D&background=EEEEEE' }}"
                             alt="{{ $share->post->user->name }}">
                    </a>
                    <div class="flex-1">
                        <div class="flex items-center space-x-2">
                            <a href="{{ route('profile.user', $share->post->user) }}" class="font-medium text-gray-900 hover:text-custom-green">
                                {{ $share->post->user->name }}
                            </a>
                        </div>
                        <div class="text-sm text-gray-500">
                            {{ $share->post->published_at->diffForHumans() }}
                        </div>
                    </div>
                @endif
            </div>
        </div>

        <!-- Original Post Content -->
        <div class="p-4 bg-white">
            <a href="{{ route('posts.show', $share->post) }}" class="block hover:bg-gray-50 transition-colors rounded p-2 -m-2">
                <!-- Post Method and Tags -->
                @if($share->post->postMethod || $share->post->tags->count() > 0)
                    <div class="mb-2">
                        <div class="flex flex-wrap items-center gap-1">
                            @if($share->post->postMethod)
                                <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                    {{ $share->post->postMethod->name }}
                                </span>
                            @endif
                            @foreach($share->post->tags as $tag)
                                <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium text-white"
                                      style="background-color: {{ $tag->color }}">
                                    {{ $tag->name }}
                                </span>
                            @endforeach
                        </div>
                    </div>
                @endif

                <div class="flex items-start justify-between">
                    <h3 class="text-lg font-semibold text-gray-900 mb-2 flex-1">{{ $share->post->title }}</h3>
                    <svg class="w-5 h-5 text-gray-400 ml-2 mt-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                    </svg>
                </div>

                @if($share->post->content)
                    <div class="text-gray-700 mb-3">
                        <p>{!! nl2br(e($share->post->content)) !!}</p>
                    </div>
                @endif

                <!-- Post Images -->
                @if($share->post->images && count($share->post->images) > 0)
                    <div class="mb-3">
                        @if(count($share->post->images) == 1)
                            <div class="rounded-lg overflow-hidden">
                                <img src="{{ \Illuminate\Support\Facades\Storage::disk('public')->url($share->post->images[0]) }}" 
                                     alt="Post image" 
                                     class="w-full h-64 object-cover">
                            </div>
                        @else
                            <div class="grid grid-cols-2 gap-2 rounded-lg overflow-hidden">
                                @foreach(array_slice($share->post->images, 0, 4) as $index => $image)
                                    <div class="relative {{ $index >= 2 ? 'hidden sm:block' : '' }}">
                                        <img src="{{ \Illuminate\Support\Facades\Storage::disk('public')->url($image) }}" 
                                             alt="Post image {{ $index + 1 }}" 
                                             class="w-full h-32 object-cover">
                                        @if($index == 3 && count($share->post->images) > 4)
                                            <div class="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                                                <span class="text-white font-semibold">+{{ count($share->post->images) - 4 }}</span>
                                            </div>
                                        @endif
                                    </div>
                                @endforeach
                            </div>
                        @endif
                    </div>
                @endif

                <!-- Post Type Badge -->
                @if($share->post->type !== 'general')
                    <div class="mb-3">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                            @if($share->post->type === 'announcement') bg-blue-100 text-blue-800
                            @elseif($share->post->type === 'event') bg-green-100 text-green-800
                            @elseif($share->post->type === 'job') bg-purple-100 text-purple-800
                            @elseif($share->post->type === 'scholarship') bg-yellow-100 text-yellow-800
                            @else bg-gray-100 text-gray-800
                            @endif">
                            {{ ucfirst($share->post->type) }}
                        </span>
                    </div>
                @endif
            </a>
        </div>
    </div>

    <!-- Reaction Summary Bar (matching original post layout) -->
    @php
        $totalShareReactions = $share->reactions()->count();
        $totalShareComments = $share->comments->count();
        $totalOriginalShares = $share->post->shares->count();

        // Get top 3 reaction types for share
        $topShareReactions = $share->reactions()
            ->select('type', \DB::raw('count(*) as count'))
            ->groupBy('type')
            ->orderBy('count', 'desc')
            ->limit(3)
            ->get();
    @endphp

    <div id="share-summary-bar-{{ $share->id }}" class="px-4 py-3 border-t border-gray-200" style="display: {{ ($totalShareReactions > 0 || $totalShareComments > 0 || $totalOriginalShares > 0) ? 'block' : 'none' }}">
        <div class="flex items-center justify-between text-sm text-gray-600">
            <!-- Left: Share reaction emojis and count -->
            <div class="flex items-center space-x-2">
                <div id="share-reaction-summary-{{ $share->id }}" class="flex items-center space-x-1" style="display: {{ $totalShareReactions > 0 ? 'flex' : 'none' }}">
                    <!-- Show top reaction emojis -->
                    <div id="share-reaction-emojis-{{ $share->id }}" class="flex -space-x-1">
                        @foreach($topShareReactions as $reaction)
                            @php
                                $reactionDetails = \App\Models\Reaction::getReactionDetails($reaction->type);
                            @endphp
                            @if($reactionDetails)
                                <img src="{{ $reactionDetails['emoji'] }}" alt="{{ $reactionDetails['label'] }}" class="w-5 h-5 rounded-full border border-white" onerror="this.style.display='none';">
                            @endif
                        @endforeach
                    </div>
                    <span id="share-reaction-count-{{ $share->id }}" class="text-gray-600 hover:underline cursor-pointer">{{ $totalShareReactions }}</span>
                </div>
            </div>

            <!-- Right: Comments and shares -->
            <div class="flex items-center space-x-4">
                <span id="share-comment-summary-{{ $share->id }}" class="hover:underline cursor-pointer" onclick="openShareCommentModal({{ $share->id }})" style="display: {{ $totalShareComments > 0 ? 'inline' : 'none' }}">
                    {{ $totalShareComments }} comment{{ $totalShareComments !== 1 ? 's' : '' }}
                </span>
                <span id="original-share-summary-{{ $share->id }}" style="display: {{ $totalOriginalShares > 0 ? 'inline' : 'none' }}">
                    {{ $totalOriginalShares }} share{{ $totalOriginalShares !== 1 ? 's' : '' }}
                </span>
            </div>
        </div>
    </div>

    <!-- Action Buttons (matching original post layout) -->
    <div class="px-4 py-3 border-t border-gray-200">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-1">
                <!-- Facebook-style Reactions for Share -->
                <x-facebook-reactions :target="$share" target-type="share" :show-count="false" />

                <!-- Comment Button (comments on the SHARE) -->
                <button onclick="openShareCommentModal({{ $share->id }})" class="flex items-center space-x-2 text-gray-500 hover:text-blue-600 transition-colors py-2 px-4 rounded-lg hover:bg-gray-100">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                    </svg>
                    <span class="text-sm font-medium">Comment</span>
                </button>

                <!-- Share Button (share the original post) -->
                <button onclick="openShareModal({{ $share->post->id }})" class="flex items-center space-x-2 text-gray-500 hover:text-green-600 transition-colors py-2 px-4 rounded-lg hover:bg-gray-100">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                    </svg>
                    <span class="text-sm font-medium">Share</span>
                </button>
            </div>

            <!-- View Original Post Button (right side) -->
            <button onclick="viewOriginalPost({{ $share->post->id }})"
                    class="flex items-center space-x-2 text-gray-500 hover:text-purple-600 transition-colors py-2 px-3 rounded-lg hover:bg-gray-100">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                </svg>
                <span class="text-xs font-medium">View Original</span>
            </button>
        </div>
    </div>

    <!-- Inline Comments Section for SHARE (hidden by default) -->
    <div id="share-comments-section-{{ $share->id }}" class="hidden border-t border-gray-200">
        <x-share-comment-section :share="$share" :showInline="true" />
    </div>

    <!-- Share Comment Modal -->
    <x-share-comment-modal :share="$share" />

    <!-- Share Modal -->
    <x-share-modal :post="$share->post" />
</div>
